var targetModule = "AMG";
var targetOffset = 0x9062c4

function attachToTargetModule() {
    Process.enumerateModules({
        onMatch: function(module) {
            if (module.name === targetModule) {
                console.log("[*] 模块名称: " + module.name);
                console.log("[*] 模块基址: " + module.base);

                var targetAddress = module.base.add(targetOffset);
                console.log("[*] Hook 地址: " + targetAddress);

                Interceptor.attach(targetAddress, {
                    onEnter: function(args) {
                        console.log("[+]函数被调用");
                        console.log(Thread.backtrace(this.context, Backtracer.FUZZY)
                            .map(addr => {
                                const m = Process.findModuleByAddress(addr);
                                if (m) {
                                    const offset = addr.sub(m.base);
                                    return `${m.name}!0x${addr.toString(16)} -> IDA偏移: 0x${offset.toString(16)}`;
                                }
                                return `0x${addr.toString(16)}`;
                            })
                            .join('\n')
                        );
                        console.log("[+] ---------------------------------------------------------------");




                    },

                    onLeave: function(retval) {
                       
                    }
                });
            }
        },
        onComplete: function() {
            console.log("[*] 模块枚举完成");
        }
    });
}

setTimeout(attachToTargetModule, 100);